#!/usr/bin/env python3
"""
Build search index for the ComplianceMax project.
Creates manifest.json and files.txt for efficient searching.
"""

import json
import os
import hashlib
from pathlib import Path
from datetime import datetime
import mimetypes

# Define directories to exclude
EXCLUDE_DIRS = {
    '.git', '__pycache__', 'node_modules', '.pytest_cache', 
    '.venv', 'venv', 'env', '.env', 'dist', 'build',
    '.index', 'coverage', '.coverage', 'htmlcov',
    '.mypy_cache', '.tox', '.eggs', '*.egg-info'
}

# Define file extensions to index
INCLUDE_EXTENSIONS = {
    '.py', '.js', '.jsx', '.ts', '.tsx', '.json', '.yml', '.yaml',
    '.md', '.txt', '.sh', '.bash', '.sql', '.html', '.css', '.scss',
    '.dockerfile', '.dockerignore', '.gitignore', '.env.example',
    '.toml', '.ini', '.cfg', '.conf', '.xml'
}

def should_index_file(file_path):
    """Determine if a file should be indexed."""
    path = Path(file_path)
    
    # Skip if in excluded directory
    for part in path.parts:
        if part in EXCLUDE_DIRS:
            return False
    
    # Check extension
    if path.suffix.lower() in INCLUDE_EXTENSIONS:
        return True
    
    # Check for files without extensions (like Dockerfile)
    if path.name.lower() in ['dockerfile', 'makefile', 'readme']:
        return True
    
    return False

def get_file_hash(file_path):
    """Calculate SHA256 hash of file content."""
    sha256_hash = hashlib.sha256()
    try:
        with open(file_path, "rb") as f:
            for byte_block in iter(lambda: f.read(4096), b""):
                sha256_hash.update(byte_block)
        return sha256_hash.hexdigest()
    except Exception:
        return None

def build_index(root_dir="."):
    """Build the search index."""
    root_path = Path(root_dir).resolve()
    manifest = {
        "version": "1.0",
        "created": datetime.now().isoformat(),
        "root": str(root_path),
        "files": {}
    }
    
    file_list = []
    
    print(f"Building index from: {root_path}")
    
    for file_path in root_path.rglob("*"):
        if file_path.is_file() and should_index_file(file_path):
            relative_path = file_path.relative_to(root_path)
            
            try:
                stats = file_path.stat()
                file_info = {
                    "path": str(relative_path),
                    "size": stats.st_size,
                    "modified": datetime.fromtimestamp(stats.st_mtime).isoformat(),
                    "hash": get_file_hash(file_path),
                    "mime_type": mimetypes.guess_type(str(file_path))[0] or "text/plain"
                }
                
                manifest["files"][str(relative_path)] = file_info
                file_list.append(str(relative_path))
                
            except Exception as e:
                print(f"Error processing {file_path}: {e}")
    
    # Create .index directory if it doesn't exist
    index_dir = Path(".index")
    index_dir.mkdir(exist_ok=True)
    
    # Write manifest.json
    manifest_path = index_dir / "manifest.json"
    with open(manifest_path, "w") as f:
        json.dump(manifest, f, indent=2)
    
    # Write files.txt
    files_path = index_dir / "files.txt"
    with open(files_path, "w") as f:
        f.write("\n".join(sorted(file_list)))
    
    print(f"\nIndex created successfully!")
    print(f"Total files indexed: {len(file_list)}")
    print(f"Manifest: {manifest_path}")
    print(f"File list: {files_path}")
    
    return len(file_list)

if __name__ == "__main__":
    build_index()