name: Comprehensive Health Check and Monitoring

on:
  push:
    branches: [ main, develop, feat/health-check-overhaul ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run health checks every 6 hours
    - cron: '0 */6 * * *'
  workflow_dispatch:
    inputs:
      health_check_type:
        description: 'Type of health check to run'
        required: true
        default: 'comprehensive'
        type: choice
        options:
          - comprehensive
          - quick
          - compliance-only
          - ml-only

env:
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1
  HEALTH_CHECK_TIMEOUT: 300

jobs:
  # Infrastructure health checks
  infrastructure-health:
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_DB: compliancemax_test
          POSTGRES_USER: compliancemax
          POSTGRES_PASSWORD: test123
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 3s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install psycopg2-binary redis pytest asyncio aiohttp

      - name: Test database connectivity
        run: |
          python -c "
          import psycopg2
          import time
          
          max_retries = 30
          for i in range(max_retries):
              try:
                  conn = psycopg2.connect(
                      host='localhost',
                      port=5432,
                      database='compliancemax_test',
                      user='compliancemax',
                      password='test123'
                  )
                  cursor = conn.cursor()
                  cursor.execute('SELECT 1')
                  result = cursor.fetchone()
                  print(f'Database connectivity test passed: {result}')
                  conn.close()
                  break
              except Exception as e:
                  print(f'Attempt {i+1}/{max_retries} failed: {e}')
                  if i == max_retries - 1:
                      raise
                  time.sleep(2)
          "

      - name: Test Redis connectivity
        run: |
          python -c "
          import redis
          import time
          
          max_retries = 30
          for i in range(max_retries):
              try:
                  r = redis.Redis(host='localhost', port=6379, decode_responses=True)
                  r.ping()
                  r.set('test_key', 'test_value')
                  value = r.get('test_key')
                  print(f'Redis connectivity test passed: {value}')
                  r.delete('test_key')
                  break
              except Exception as e:
                  print(f'Attempt {i+1}/{max_retries} failed: {e}')
                  if i == max_retries - 1:
                      raise
                  time.sleep(2)
          "

      - name: Upload infrastructure health report
        uses: actions/upload-artifact@v3
        with:
          name: infrastructure-health-report
          path: health_reports/
          retention-days: 30