
name: Maintenance Procedures

on:
  schedule:
    # Run weekly maintenance on Sundays at 2 AM UTC
    - cron: '0 2 * * 0'
  workflow_dispatch:
    inputs:
      maintenance_type:
        description: 'Type of maintenance to perform'
        required: true
        default: 'routine'
        type: choice
        options:
        - routine
        - security
        - performance
        - full
      force_updates:
        description: 'Force dependency updates'
        required: false
        default: false
        type: boolean

jobs:
  routine-maintenance:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
        cache-dependency-path: backend/requirements.txt
        
    - name: Install maintenance tools
      run: |
        npm install -g npm-check-updates
        pip install pip-tools safety bandit
        
    - name: Check for dependency updates
      run: |
        echo "🔍 Checking for dependency updates..."
        
        # Frontend dependency check
        cd frontend
        echo "## Frontend Dependencies" > ../maintenance_report.md
        echo "### Current Status" >> ../maintenance_report.md
        npm outdated >> ../maintenance_report.md || echo "No outdated packages" >> ../maintenance_report.md
        
        echo "### Available Updates" >> ../maintenance_report.md
        ncu >> ../maintenance_report.md || echo "All packages up to date" >> ../maintenance_report.md
        
        # Backend dependency check
        cd ../backend
        echo "## Backend Dependencies" >> ../maintenance_report.md
        echo "### Current Status" >> ../maintenance_report.md
        pip list --outdated >> ../maintenance_report.md || echo "No outdated packages" >> ../maintenance_report.md
        
    - name: Security audit
      run: |
        echo "🔒 Running security audit..."
        
        echo "## Security Audit" >> maintenance_report.md
        
        # Frontend security audit
        cd frontend
        echo "### Frontend Security" >> ../maintenance_report.md
        npm audit --audit-level=moderate >> ../maintenance_report.md || echo "No security issues found" >> ../maintenance_report.md
        
        # Backend security audit
        cd ../backend
        echo "### Backend Security" >> ../maintenance_report.md
        safety check >> ../maintenance_report.md || echo "No known vulnerabilities" >> ../maintenance_report.md
        
        # Static security analysis
        echo "### Static Analysis" >> ../maintenance_report.md
        bandit -r . -f txt >> ../maintenance_report.md || echo "No security issues found" >> ../maintenance_report.md
        
    - name: Performance analysis
      if: github.event.inputs.maintenance_type == 'performance' || github.event.inputs.maintenance_type == 'full'
      run: |
        echo "📊 Running performance analysis..."
        
        echo "## Performance Analysis" >> maintenance_report.md
        
        # Analyze bundle sizes
        cd frontend
        echo "### Frontend Bundle Analysis" >> ../maintenance_report.md
        npm run build 2>&1 | grep -E "(Size|Parsed|Gzipped)" >> ../maintenance_report.md || echo "Bundle analysis not available" >> ../maintenance_report.md
        
        # Check for large files
        cd ..
        echo "### Large Files Check" >> maintenance_report.md
        find . -type f -size +1M -not -path "./node_modules/*" -not -path "./.git/*" >> maintenance_report.md || echo "No large files found" >> maintenance_report.md
        
    - name: Update dependencies
      if: github.event.inputs.force_updates == 'true'
      run: |
        echo "🔄 Updating dependencies..."
        
        # Update frontend dependencies
        cd frontend
        npm update
        
        # Update backend dependencies (careful approach)
        cd ../backend
        pip-compile --upgrade requirements.in || echo "No requirements.in found, skipping pip-compile"
        
    - name: Run health check after maintenance
      run: |
        echo "🏥 Running post-maintenance health check..."
        
        # Install dependencies
        cd frontend && npm ci
        cd ../backend && pip install -r requirements.txt
        
        # Start services
        nohup python -m uvicorn main:app --host 0.0.0.0 --port 8001 &
        BACKEND_PID=$!

        cd ../frontend
        npm run build
        nohup npm start -- --port 3001 &
        FRONTEND_PID=$!
        
        # Wait for services
        sleep 30
        
        # Run health check
        cd ..
        python scripts/health_check.py --detailed >> maintenance_report.md
        
        # Cleanup
        kill $BACKEND_PID $FRONTEND_PID || true
        
    - name: Generate maintenance summary
      if: always()
      run: |
        echo "📋 Generating maintenance summary..."
        
        # Add header to report
        cat > final_maintenance_report.md << EOF
        # Maintenance Report
        
        **Date:** $(date)
        **Type:** ${{ github.event.inputs.maintenance_type || 'routine' }}
        **Run ID:** ${{ github.run_id }}
        **Force Updates:** ${{ github.event.inputs.force_updates || 'false' }}
        
        ---
        
        EOF
        
        # Append existing report
        cat maintenance_report.md >> final_maintenance_report.md
        
        # Add to GitHub Step Summary
        cat final_maintenance_report.md >> $GITHUB_STEP_SUMMARY
        
    - name: Create maintenance issue
      if: failure()
      run: |
        echo "🚨 Creating maintenance issue due to failures..."
        
        # This would create a GitHub issue with maintenance failures
        # For now, we'll just log the intent
        echo "Maintenance issue would be created here with details from maintenance_report.md"
        
    - name: Upload maintenance artifacts
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: maintenance-report-${{ github.run_number }}
        path: |
          final_maintenance_report.md
          maintenance_report.md
        retention-days: 90

  cleanup-old-artifacts:
    runs-on: ubuntu-latest
    needs: routine-maintenance
    
    steps:
    - name: Cleanup old artifacts
      run: |
        echo "🧹 Cleaning up old artifacts..."
        
        # This would use GitHub API to clean up old artifacts
        # For now, we'll just document the process
        
        echo "## Artifact Cleanup" >> cleanup_report.md
        echo "**Date:** $(date)" >> cleanup_report.md
        echo "" >> cleanup_report.md
        echo "Artifact cleanup process would run here to remove:" >> cleanup_report.md
        echo "- Health check logs older than 30 days" >> cleanup_report.md
        echo "- Monitoring results older than 90 days" >> cleanup_report.md
        echo "- Build artifacts older than 7 days" >> cleanup_report.md
        
        # Add to GitHub Step Summary
        cat cleanup_report.md >> $GITHUB_STEP_SUMMARY

