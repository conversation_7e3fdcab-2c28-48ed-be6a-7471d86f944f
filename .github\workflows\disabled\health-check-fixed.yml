
name: ComplianceMax Health Check

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run health check daily at 6 AM UTC
    - cron: '0 6 * * *'
  workflow_dispatch:
    inputs:
      detailed:
        description: 'Run detailed health check'
        required: false
        default: false
        type: boolean
      fix_issues:
        description: 'Attempt to fix issues automatically'
        required: false
        default: false
        type: boolean

jobs:
  health-check:
    runs-on: ubuntu-latest
    timeout-minutes: 20
    continue-on-error: false
    
    strategy:
      fail-fast: false
      matrix:
        node-version: [18.x, 20.x]
        python-version: [3.9, 3.11]
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Set up Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pip'
        cache-dependency-path: backend/requirements.txt
        
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y redis-server postgresql postgresql-contrib
        sudo systemctl start redis-server
        sudo systemctl start postgresql
        
        # Create PostgreSQL database and user
        sudo -u postgres createuser --createdb --login compliancemax || true
        sudo -u postgres createdb compliancemax_test || true
        
    - name: Install Python dependencies
      run: |
        cd backend
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: Install Node.js dependencies
      run: |
        cd frontend
        npm ci --prefer-offline --no-audit
        
    - name: Create environment files
      run: |
        # Copy environment files
        cp .env.example .env || echo "No .env.example found, creating basic .env"
        
        # Create basic .env if it doesn't exist
        if [ ! -f .env ]; then
          cat > .env << EOF
        NODE_ENV=test
        DEBUG=false
        DATABASE_URL=postgresql://compliancemax:password@localhost:5432/compliancemax_test
        REDIS_URL=redis://localhost:6379
        SECRET_KEY=test-secret-key-for-ci
        FRONTEND_URL=http://localhost:3001
        BACKEND_URL=http://localhost:8001
        EOF
        fi
        
        # Frontend environment
        cd frontend
        if [ -f ../.env.development ]; then
          cp ../.env.development .env.local
        else
          cat > .env.local << EOF
        NEXT_PUBLIC_API_URL=http://localhost:8001
        NEXT_PUBLIC_WS_URL=ws://localhost:8001
        NODE_ENV=test
        EOF
        fi
        
        # Backend environment
        cd ../backend
        cp ../.env .env || echo "Using default backend env"
        
    - name: Lint and type check
      run: |
        # Frontend linting
        cd frontend
        npm run lint || echo "Linting issues found but continuing"
        
        # Backend linting (if available)
        cd ../backend
        python -m flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics || echo "Backend linting issues found but continuing"
        
    - name: Run tests
      run: |
        # Frontend tests
        cd frontend
        npm test -- --passWithNoTests --watchAll=false || echo "Frontend tests failed but continuing"
        
        # Backend tests
        cd ../backend
        python -m pytest --tb=short -v || echo "Backend tests failed but continuing"
        
    - name: Build frontend
      run: |
        cd frontend
        npm run build
        
    - name: Start backend service
      run: |
        nohup python -m uvicorn main:app --host 0.0.0.0 --port 8001 --reload &
        echo $! > backend.pid
        sleep 15
        
    - name: Start frontend service
      run: |
        cd frontend
        nohup npm start -- --port 3001 &
        echo $! > frontend.pid
        sleep 20
        
    - name: Wait for services to be ready
      run: |
        echo "Waiting for backend health endpoint..."
        timeout 120 bash -c 'until curl -f http://localhost:8001/health; do echo "Waiting for backend..."; sleep 3; done'
        
        echo "Waiting for frontend..."
        timeout 120 bash -c 'until curl -f http://localhost:3001; do echo "Waiting for frontend..."; sleep 3; done'
        
    - name: Run Health Check
      run: |
        python scripts/health_check.py \
          ${{ github.event.inputs.detailed == 'true' && '--detailed' || '' }} \
          ${{ github.event.inputs.fix_issues == 'true' && '--fix-issues' || '' }} \
          --json > health-check-results.json
          
        # Display results
        cat health-check-results.json
        
    - name: Validate API endpoints
      run: |
        echo "Testing critical API endpoints..."
        
        # Test backend health
        curl -f http://localhost:8001/health || exit 1
        
        # Test API health
        curl -f http://localhost:8001/api/health || exit 1
        
        # Test frontend
        curl -f http://localhost:3001 || exit 1
        
        echo "All critical endpoints are responding"
        
    - name: Stop services
      if: always()
      run: |
        # Stop services gracefully
        if [ -f backend/backend.pid ]; then
          kill $(cat backend/backend.pid) || true
        fi
        if [ -f frontend/frontend.pid ]; then
          kill $(cat frontend/frontend.pid) || true
        fi
        
        # Force kill if needed
        pkill -f "uvicorn main:app" || true
        pkill -f "npm start" || true
        
    - name: Upload health check logs
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: health-check-logs-${{ matrix.node-version }}-${{ matrix.python-version }}
        path: |
          health-check-results.json
          logs/
          frontend/npm-debug.log*
          backend/*.log
        retention-days: 7
        
    - name: Create health check report
      if: always()
      run: |
        echo "## Health Check Report" >> $GITHUB_STEP_SUMMARY
        echo "**Node.js Version:** ${{ matrix.node-version }}" >> $GITHUB_STEP_SUMMARY
        echo "**Python Version:** ${{ matrix.python-version }}" >> $GITHUB_STEP_SUMMARY
        echo "**Timestamp:** $(date)" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        if [ -f health-check-results.json ]; then
          echo "### Health Check Results" >> $GITHUB_STEP_SUMMARY
          echo '```json' >> $GITHUB_STEP_SUMMARY
          head -50 health-check-results.json >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
        fi
        
        # Add service status
        echo "### Service Status" >> $GITHUB_STEP_SUMMARY
        echo "- Backend: $(curl -s http://localhost:8001/health && echo 'HEALTHY' || echo 'UNHEALTHY')" >> $GITHUB_STEP_SUMMARY
        echo "- Frontend: $(curl -s http://localhost:3001 && echo 'HEALTHY' || echo 'UNHEALTHY')" >> $GITHUB_STEP_SUMMARY
        
    - name: Notify on failure
      if: failure()
      run: |
        echo "❌ Health check failed for Node.js ${{ matrix.node-version }} and Python ${{ matrix.python-version }}"
        echo "Check the logs and artifacts for detailed information."
        
        # Create failure summary
        echo "## ❌ Health Check Failure" >> $GITHUB_STEP_SUMMARY
        echo "**Configuration:** Node.js ${{ matrix.node-version }}, Python ${{ matrix.python-version }}" >> $GITHUB_STEP_SUMMARY
        echo "**Time:** $(date)" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "Please check the uploaded artifacts for detailed logs." >> $GITHUB_STEP_SUMMARY

  security-scan:
    runs-on: ubuntu-latest
    needs: health-check
    if: github.event_name == 'push' || github.event_name == 'pull_request'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Run security scan
      run: |
        echo "🔍 Running security scan..."
        
        # Check for sensitive files
        echo "Scanning for sensitive files..."
        find . -name "*.key" -o -name "*.pem" -o -name "*.p12" -o -name "*.pfx" | grep -v node_modules || echo "No sensitive files found"
        
        # Check for hardcoded secrets (basic patterns)
        echo "Scanning for potential secrets..."
        grep -r -i "password.*=" . --include="*.py" --include="*.js" --include="*.ts" --exclude-dir=node_modules --exclude-dir=.git || echo "No hardcoded passwords found"
        grep -r -i "secret.*=" . --include="*.py" --include="*.js" --include="*.ts" --exclude-dir=node_modules --exclude-dir=.git || echo "No hardcoded secrets found"
        
        # Check for API keys
        echo "Scanning for API keys..."
        grep -r -i "api[_-]key" . --include="*.py" --include="*.js" --include="*.ts" --exclude-dir=node_modules --exclude-dir=.git || echo "No hardcoded API keys found"
        
    - name: Check environment security
      run: |
        if [ -f .env ]; then
          echo "🔒 Checking .env file security..."
          # Check for common insecure patterns
          if grep -i "password=123\|secret=test\|key=abc\|password=password" .env; then
            echo "❌ Insecure patterns found in .env file"
            exit 1
          else
            echo "✅ .env file appears secure"
          fi
        else
          echo "⚠️ No .env file found"
        fi
        
    - name: Dependency security check
      run: |
        echo "🔍 Checking for known vulnerabilities..."
        
        # Frontend dependency check
        cd frontend
        npm audit --audit-level=high || echo "Frontend vulnerabilities found but not blocking"
        
        # Backend dependency check
        cd ../backend
        pip install safety
        safety check || echo "Backend vulnerabilities found but not blocking"

  performance-test:
    runs-on: ubuntu-latest
    needs: health-check
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
        cache-dependency-path: backend/requirements.txt
        
    - name: Install dependencies
      run: |
        cd backend && pip install -r requirements.txt
        cd ../frontend && npm ci --prefer-offline --no-audit
        
    - name: Build and start services
      run: |
        # Build frontend
        cd frontend && npm run build
        
        # Start backend
        cd .. && nohup python -m uvicorn main:app --host 0.0.0.0 --port 8001 &
        
        # Start frontend
        cd frontend && nohup npm start -- --port 3001 &
        
        # Wait for services
        sleep 30
        
    - name: Run performance tests
      run: |
        echo "🚀 Running performance tests..."
        
        # Install performance testing tools
        sudo apt-get update && sudo apt-get install -y apache2-utils
        
        # Test API response times
        echo "Testing API response times..."
        for i in {1..5}; do
          echo "Test $i:"
          curl -w "Response time: %{time_total}s, Status: %{http_code}\n" -o /dev/null -s http://localhost:8001/health
          curl -w "Response time: %{time_total}s, Status: %{http_code}\n" -o /dev/null -s http://localhost:3001
        done
        
    - name: Load test
      run: |
        echo "🔥 Running basic load test..."
        
        # Simple concurrent request test using ab (Apache Bench)
        echo "Load testing backend health endpoint..."
        ab -n 100 -c 10 http://localhost:8001/health/ || echo "Load test completed with some errors"
        
        echo "Load testing frontend..."
        ab -n 50 -c 5 http://localhost:3001/ || echo "Frontend load test completed with some errors"
        
        echo "Load test completed"
        
    - name: Performance report
      if: always()
      run: |
        echo "## 📊 Performance Test Results" >> $GITHUB_STEP_SUMMARY
        echo "**Timestamp:** $(date)" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "Performance tests completed. Check logs for detailed metrics." >> $GITHUB_STEP_SUMMARY

