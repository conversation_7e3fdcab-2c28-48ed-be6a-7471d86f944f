name: ComplianceMax Health Check

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run health check daily at 6 AM UTC
    - cron: '0 6 * * *'
  workflow_dispatch:
    inputs:
      detailed:
        description: 'Run detailed health check'
        required: false
        default: false
        type: boolean
      fix_issues:
        description: 'Attempt to fix issues automatically'
        required: false
        default: false
        type: boolean

jobs:
  health-check:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
        python-version: [3.9, 3.11]
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Set up Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pip'
        cache-dependency-path: backend/requirements.txt
        
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y redis-server postgresql postgresql-contrib
        sudo systemctl start redis-server
        sudo systemctl start postgresql
        
    - name: Install Python dependencies
      run: |
        cd backend
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: Install Node.js dependencies
      run: |
        cd frontend
        npm ci
        
    - name: Create environment files
      run: |
        cp .env.example .env
        cd frontend && cp ../.env.development .env.local
        cd ../backend && cp ../.env .env
        
    - name: Start backend service
      run: |
        cd backend
        nohup uvicorn backend.app.main:app --host 0.0.0.0 --port 8001 &
        sleep 10
        
    - name: Start frontend service
      run: |
        cd frontend
        nohup npm run dev -- --port 3001 &
        sleep 15
        
    - name: Wait for services to be ready
      run: |
        timeout 60 bash -c 'until curl -f http://localhost:8001/health; do sleep 2; done'
        timeout 60 bash -c 'until curl -f http://localhost:3001; do sleep 2; done'
        
    - name: Run Health Check
      run: |
        python scripts/health_check.py \
          ${{ github.event.inputs.detailed == 'true' && '--detailed' || '' }} \
          ${{ github.event.inputs.fix_issues == 'true' && '--fix-issues' || '' }} \
          --json
          
    - name: Upload health check logs
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: health-check-logs-${{ matrix.node-version }}-${{ matrix.python-version }}
        path: |
          logs/health_check.log
          logs/
        retention-days: 7
        
    - name: Create health check report
      if: always()
      run: |
        echo "## Health Check Report" >> $GITHUB_STEP_SUMMARY
        echo "**Node.js Version:** ${{ matrix.node-version }}" >> $GITHUB_STEP_SUMMARY
        echo "**Python Version:** ${{ matrix.python-version }}" >> $GITHUB_STEP_SUMMARY
        echo "**Timestamp:** $(date)" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        if [ -f logs/health_check.log ]; then
          echo "### Health Check Log (Last 20 lines)" >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          tail -20 logs/health_check.log >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
        fi
        
    - name: Notify on failure
      if: failure()
      run: |
        echo "❌ Health check failed for Node.js ${{ matrix.node-version }} and Python ${{ matrix.python-version }}"
        echo "Check the logs and artifacts for detailed information."
        
  security-scan:
    runs-on: ubuntu-latest
    needs: health-check
    if: github.event_name == 'push' || github.event_name == 'pull_request'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Run security scan
      run: |
        # Check for sensitive files
        echo "Scanning for sensitive files..."
        find . -name "*.key" -o -name "*.pem" -o -name "*.p12" -o -name "*.pfx" | grep -v node_modules || true
        
        # Check for hardcoded secrets (basic patterns)
        echo "Scanning for potential secrets..."
        grep -r -i "password.*=" . --include="*.py" --include="*.js" --include="*.ts" --exclude-dir=node_modules --exclude-dir=.git || true
        grep -r -i "secret.*=" . --include="*.py" --include="*.js" --include="*.ts" --exclude-dir=node_modules --exclude-dir=.git || true
        
    - name: Check environment security
      run: |
        if [ -f .env ]; then
          echo "Checking .env file security..."
          # Check for common insecure patterns
          if grep -i "password=123\|secret=test\|key=abc" .env; then
            echo "❌ Insecure patterns found in .env file"
            exit 1
          else
            echo "✅ .env file appears secure"
          fi
        fi

  performance-test:
    runs-on: ubuntu-latest
    needs: health-check
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install dependencies
      run: |
        cd backend && pip install -r requirements.txt
        cd ../frontend && npm ci
        
    - name: Start services
      run: |
        cd backend && nohup uvicorn backend.app.main:app --host 0.0.0.0 --port 8001 &
        cd frontend && nohup npm run dev -- --port 3001 &
        sleep 20
        
    - name: Run performance tests
      run: |
        # Basic performance test using curl
        echo "Testing API response times..."
        
        for i in {1..5}; do
          echo "Test $i:"
          curl -w "Response time: %{time_total}s\n" -o /dev/null -s http://localhost:8001/health
          curl -w "Response time: %{time_total}s\n" -o /dev/null -s http://localhost:3001
        done
        
    - name: Load test
      run: |
        # Simple concurrent request test
        echo "Running basic load test..."
        
        for i in {1..10}; do
          curl -s http://localhost:8001/health &
        done
        wait
        
        echo "Load test completed"
