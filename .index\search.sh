#!/bin/bash
# Search helper script for ComplianceMax project

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to display usage
usage() {
    echo "Usage: $0 <search-type> <pattern>"
    echo ""
    echo "Search types:"
    echo "  text    - Full text search in all indexed files"
    echo "  file    - Search for files by name pattern"
    echo "  type    - Search for files by extension (e.g., .py, .js)"
    echo ""
    echo "Examples:"
    echo "  $0 text 'compliance_rules'"
    echo "  $0 file 'test_'"
    echo "  $0 type '.py'"
    exit 1
}

# Check arguments
if [ $# -lt 2 ]; then
    usage
fi

SEARCH_TYPE=$1
PATTERN=$2
INDEX_DIR="$(dirname "$0")"

case $SEARCH_TYPE in
    text)
        echo -e "${GREEN}Searching for text: ${YELLOW}$PATTERN${NC}"
        if [ -f "$INDEX_DIR/files.txt" ]; then
            # Check if ripgrep is available
            if command -v rg &> /dev/null; then
                rg --files-from "$INDEX_DIR/files.txt" "$PATTERN" --color=always
            else
                # Fallback to grep
                echo -e "${YELLOW}Note: Using grep (install ripgrep for better performance)${NC}"
                while IFS= read -r file; do
                    if [ -f "$file" ]; then
                        grep -Hn --color=always "$PATTERN" "$file" 2>/dev/null
                    fi
                done < "$INDEX_DIR/files.txt"
            fi
        else
            echo -e "${RED}Error: files.txt not found. Run build_index.py first.${NC}"
            exit 1
        fi
        ;;
    file)
        echo -e "${GREEN}Searching for files matching: ${YELLOW}$PATTERN${NC}"
        if [ -f "$INDEX_DIR/files.txt" ]; then
            grep -i "$PATTERN" "$INDEX_DIR/files.txt" | head -20
        else
            echo -e "${RED}Error: files.txt not found. Run build_index.py first.${NC}"
            exit 1
        fi
        ;;
    type)
        echo -e "${GREEN}Searching for files with extension: ${YELLOW}$PATTERN${NC}"
        if [ -f "$INDEX_DIR/files.txt" ]; then
            grep -E "${PATTERN}$" "$INDEX_DIR/files.txt" | head -20
        else
            echo -e "${RED}Error: files.txt not found. Run build_index.py first.${NC}"
            exit 1
        fi
        ;;
    *)
        usage
        ;;
esac