{"9988689487030119851": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["frontend"], "target": "start"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/compliancemax/apps/frontend-e2e/test-output", "{workspaceRoot}/dist/.playwright/compliancemax/apps/frontend-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=..\\..\\..\\dist\\.playwright\\compliancemax\\apps\\frontend-e2e\\test-output\\src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "..\\..\\..\\dist\\.playwright\\compliancemax\\apps\\frontend-e2e\\playwright-report\\src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "..\\..\\..\\dist\\.playwright\\compliancemax\\apps\\frontend-e2e\\playwright-report\\src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["frontend"], "target": "start"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/compliancemax/apps/frontend-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/compliancemax/apps/frontend-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/compliancemax/apps/frontend-e2e/test-output", "{workspaceRoot}/dist/.playwright/compliancemax/apps/frontend-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "1677006691783101693": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "parallelism": false, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-results", "{projectRoot}/playwright-report", "{workspaceRoot}/logs/playwright.json"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-results", "{projectRoot}/playwright-report", "{workspaceRoot}/logs/playwright.json"], "dependsOn": [], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "parallelism": false}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci"]}}}, "8800513956631946345": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["frontend"], "target": "start"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/compliancemax/apps/frontend-e2e/test-output", "{workspaceRoot}/dist/.playwright/compliancemax/apps/frontend-e2e/playwright-report"]}, "e2e-ci--src/example.spec.ts": {"command": "playwright test src/example.spec.ts --output=..\\..\\..\\dist\\.playwright\\compliancemax\\apps\\frontend-e2e\\test-output\\src-example-spec-ts", "options": {"cwd": "{projectRoot}", "env": {"PLAYWRIGHT_HTML_OUTPUT_DIR": "..\\..\\..\\dist\\.playwright\\compliancemax\\apps\\frontend-e2e\\playwright-report\\src-example-spec-ts", "PLAYWRIGHT_HTML_REPORT": "..\\..\\..\\dist\\.playwright\\compliancemax\\apps\\frontend-e2e\\playwright-report\\src-example-spec-ts"}}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in src/example.spec.ts in CI", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "dependsOn": [{"projects": ["frontend"], "target": "start"}], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/compliancemax/apps/frontend-e2e/test-output/src-example-spec-ts", "{workspaceRoot}/dist/.playwright/compliancemax/apps/frontend-e2e/playwright-report/src-example-spec-ts"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{workspaceRoot}/dist/.playwright/compliancemax/apps/frontend-e2e/test-output", "{workspaceRoot}/dist/.playwright/compliancemax/apps/frontend-e2e/playwright-report"], "dependsOn": [{"target": "e2e-ci--src/example.spec.ts", "projects": "self", "params": "forward"}], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci--src/example.spec.ts", "e2e-ci"]}}}, "2467552844318517585": {"targets": {"e2e": {"command": "playwright test", "options": {"cwd": "{projectRoot}"}, "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "parallelism": false, "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-results", "{projectRoot}/playwright-report", "{workspaceRoot}/logs/playwright.json"]}, "e2e-ci": {"executor": "nx:noop", "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["@playwright/test"]}], "outputs": ["{projectRoot}/test-results", "{projectRoot}/playwright-report", "{workspaceRoot}/logs/playwright.json"], "dependsOn": [], "metadata": {"technologies": ["playwright"], "description": "Runs Playwright Tests in CI", "nonAtomizedTarget": "e2e", "help": {"command": "npx playwright test --help", "example": {"options": {"workers": 1}}}}, "parallelism": false}}, "metadata": {"targetGroups": {"E2E (CI)": ["e2e-ci"]}}}}