
[0m[7m[1m[31m NX [39m[22m[27m[0m  [31mENOENT: no such file or directory, scandir 'C:\Users\<USER>\Documents\CBCS\AbacusGitHubRepo\00-COMPLIANCEMAX-06212025\frontend\frontend\public'[39m

Error: ENOENT: no such file or directory, scandir 'C:\Users\<USER>\Documents\CBCS\AbacusGitHubRepo\00-COMPLIANCEMAX-06212025\frontend\frontend\public'
    at readdirSync (node:fs:1584:26)
    at checkPublicDirectory (C:\Users\<USER>\Documents\CBCS\AbacusGitHubRepo\00-COMPLIANCEMAX-06212025\node_modules\@nx\next\src\executors\build\lib\check-project.js:8:35)
    at buildExecutor (C:\Users\<USER>\Documents\CBCS\AbacusGitHubRepo\00-COMPLIANCEMAX-06212025\node_modules\@nx\next\src\executors\build\build.impl.js:22:46)
    at runExecutorInternal (C:\Users\<USER>\Documents\CBCS\AbacusGitHubRepo\00-COMPLIANCEMAX-06212025\node_modules\nx\src\command-line\run\run.js:98:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async C:\Users\<USER>\Documents\CBCS\AbacusGitHubRepo\00-COMPLIANCEMAX-06212025\node_modules\nx\src\command-line\run\run.js:176:44
    at async handleErrors (C:\Users\<USER>\Documents\CBCS\AbacusGitHubRepo\00-COMPLIANCEMAX-06212025\node_modules\nx\src\utils\handle-errors.js:8:24)
    at async process.<anonymous> (C:\Users\<USER>\Documents\CBCS\AbacusGitHubRepo\00-COMPLIANCEMAX-06212025\node_modules\nx\bin\run-executor.js:59:28)

