name: Deploy ComplianceMax

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production

env:
  NODE_VERSION: '20.x'
  PYTHON_VERSION: '3.11'

jobs:
  pre-deployment-health-check:
    runs-on: ubuntu-latest
    outputs:
      health-status: ${{ steps.health-check.outputs.status }}
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
        cache-dependency-path: backend/requirements.txt
        
    - name: Install dependencies
      run: |
        cd backend && pip install -r requirements.txt
        cd ../frontend && npm ci
        
    - name: Run pre-deployment health check
      id: health-check
      run: |
        echo "Running pre-deployment health check..."
        
        # Check build processes
        cd frontend && npm run build
        cd ../backend && python -m py_compile main.py
        
        # Run static health checks
        python scripts/health_check.py --detailed --json > health-report.json
        
        # Check if health check passed
        if [ $? -eq 0 ]; then
          echo "status=passed" >> $GITHUB_OUTPUT
          echo "✅ Pre-deployment health check passed"
        else
          echo "status=failed" >> $GITHUB_OUTPUT
          echo "❌ Pre-deployment health check failed"
          exit 1
        fi
    - name: Debug – List files in workspace
      run: ls -la 
      
    - name: Upload health report
      uses: actions/upload-artifact@v4
      with:
        name: pre-deployment-health-report
        path: health-report.json
        retention-days: 30

  build:
    runs-on: ubuntu-latest
    needs: pre-deployment-health-check
    if: needs.pre-deployment-health-check.outputs.health-status == 'passed'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
        cache-dependency-path: backend/requirements.txt
        
    - name: Install frontend dependencies
      run: |
        cd frontend
        npm ci
        
    - name: Build frontend
      run: |
        cd frontend
        npm run build
        
    - name: Install backend dependencies
      run: |
        cd backend
        pip install -r requirements.txt
        
    - name: Create deployment package
      run: |
        mkdir -p deployment-package
        
        # Copy frontend build
        cp -r frontend/.next deployment-package/frontend-build
        cp frontend/package.json deployment-package/
        cp frontend/package-lock.json deployment-package/
        
        # Copy backend
        cp -r backend deployment-package/backend
        
        # Copy scripts and configuration
        cp -r scripts deployment-package/
        cp .env.example deployment-package/
        cp start_services.sh deployment-package/
        
        # Create deployment info
        echo "Build Date: $(date)" > deployment-package/build-info.txt
        echo "Commit: ${{ github.sha }}" >> deployment-package/build-info.txt
        echo "Branch: ${{ github.ref_name }}" >> deployment-package/build-info.txt
        
    - name: Upload deployment package
      uses: actions/upload-artifact@v4
      with:
        name: compliancemax-deployment-package
        path: deployment-package/
        retention-days: 30

  deploy:
    runs-on: ubuntu-latest
    needs: [pre-deployment-health-check, build]
    environment: ${{ github.event.inputs.environment || 'staging' }}
    
    steps:
    - name: Download deployment package
      uses: actions/download-artifact@v3
      with:
        name: compliancemax-deployment-package
        path: deployment/
        
    - name: Set up deployment environment
      run: |
        echo "Setting up ${{ github.event.inputs.environment || 'staging' }} environment"
        
        # Install system dependencies
        sudo apt-get update
        sudo apt-get install -y redis-server postgresql postgresql-contrib nginx
        
    - name: Deploy application
      run: |
        echo "Deploying ComplianceMax application..."
        
        # Create application directory
        sudo mkdir -p /opt/compliancemax
        sudo cp -r deployment/* /opt/compliancemax/
        
        # Set permissions
        sudo chown -R $USER:$USER /opt/compliancemax
        
        # Create systemd services (mock for this example)
        echo "Creating systemd services..."
        
    - name: Start services
      run: |
        echo "Starting ComplianceMax services..."
        
        # Start backend
        cd /opt/compliancemax/backend
        nohup uvicorn backend.app.main:app --host 0.0.0.0 --port 8001 &
        
        # Start frontend (in production, this would be served by nginx)
        cd /opt/compliancemax
        nohup npm start &
        
        # Wait for services to start
        sleep 30

  post-deployment-health-check:
    runs-on: ubuntu-latest
    needs: deploy
    
    steps:
    - name: Checkout repository (for health check script)
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Install health check dependencies
      run: |
        pip install requests websockets psutil rich
        
    - name: Run post-deployment health check
      run: |
        echo "Running post-deployment health check..."
        
        # Update health check script to use deployment URLs
        # For this example, we'll use localhost, but in real deployment
        # this would be the actual deployment URLs
        
        python scripts/health_check.py --detailed --json > post-deployment-health.json
        
        if [ $? -eq 0 ]; then
          echo "✅ Post-deployment health check passed"
          echo "🚀 Deployment successful!"
        else
          echo "❌ Post-deployment health check failed"
          echo "🚨 Deployment may have issues"
          exit 1
        fi
        
    - name: Upload post-deployment health report
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: post-deployment-health-report
        path: post-deployment-health.json
        retention-days: 30
        
    - name: Create deployment summary
      if: always()
      run: |
        echo "## Deployment Summary" >> $GITHUB_STEP_SUMMARY
        echo "**Environment:** ${{ github.event.inputs.environment || 'staging' }}" >> $GITHUB_STEP_SUMMARY
        echo "**Commit:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "**Timestamp:** $(date)" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        if [ -f post-deployment-health.json ]; then
          echo "### Health Check Results" >> $GITHUB_STEP_SUMMARY
          echo '```json' >> $GITHUB_STEP_SUMMARY
          head -20 post-deployment-health.json >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
        fi

  rollback:
    runs-on: ubuntu-latest
    needs: post-deployment-health-check
    if: failure()
    
    steps:
    - name: Rollback deployment
      run: |
        echo "🔄 Rolling back deployment due to health check failure..."
        
        # In a real scenario, this would:
        # 1. Stop current services
        # 2. Restore previous version
        # 3. Restart services
        # 4. Run health check on rolled back version
        
        echo "Rollback completed"
        
    - name: Notify team
      if: always()
      run: |
        echo "📧 Notifying team about deployment rollback..."
        # In real scenario, this would send notifications via Slack, email, etc.
