name: Enhanced Security Scanning

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run security scan daily at 2 AM UTC
    - cron: '0 2 * * *'

jobs:
  security-audit:
    runs-on: ubuntu-latest
    name: Security Audit and Scanning
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install dependencies
      run: |
        cd frontend && npm ci
        cd ../backend && pip install -r requirements.txt
        
    - name: NPM Security Audit
      run: |
        cd frontend
        npm audit --audit-level=moderate --production
        
    - name: Python Security Audit
      run: |
        cd backend
        pip install safety bandit
        safety check
        bandit -r . -f json -o bandit-report.json || true
        
    - name: SAST - Semgrep
      uses: returntocorp/semgrep-action@v1
      with:
        config: >-
          p/security-audit
          p/secrets
          p/owasp-top-ten
          
    - name: Secret Scanning
      run: |
        python3 scripts/security_audit.py > security-scan-results.json
        
    - name: Dependency Check
      run: |
        # Check for known vulnerabilities in dependencies
        cd frontend && npm audit --json > npm-audit.json || true
        cd ../backend && pip-audit --format=json --output=pip-audit.json || true
        
    - name: License Compliance
      run: |
        cd frontend && npx license-checker --json > frontend-licenses.json
        cd ../backend && pip-licenses --format=json > backend-licenses.json
        
    - name: Upload Security Reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: security-reports
        path: |
          security-scan-results.json
          frontend/npm-audit.json
          backend/pip-audit.json
          backend/bandit-report.json
          frontend/frontend-licenses.json
          backend/backend-licenses.json
          
    - name: Security Gate
      run: |
        # Fail the build if critical vulnerabilities are found
        python3 scripts/security_gate_check.py
        
  container-security:
    runs-on: ubuntu-latest
    name: Container Security Scanning
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Build Docker images
      run: |
        docker build -t compliancemax-frontend ./frontend
        docker build -t compliancemax-backend ./backend
        
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'compliancemax-frontend'
        format: 'sarif'
        output: 'trivy-frontend.sarif'
        
    - name: Run Trivy vulnerability scanner - Backend
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'compliancemax-backend'
        format: 'sarif'
        output: 'trivy-backend.sarif'
        
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-frontend.sarif'
        
  infrastructure-security:
    runs-on: ubuntu-latest
    name: Infrastructure Security
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Checkov
      uses: bridgecrewio/checkov-action@master
      with:
        directory: .
        framework: dockerfile,secrets
        output_format: sarif
        output_file_path: checkov-report.sarif
        
    - name: Upload Checkov results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: checkov-report.sarif
