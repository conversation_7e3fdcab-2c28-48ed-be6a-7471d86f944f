
name: Continuous Health Monitoring

on:
  schedule:
    # Run every 4 hours
    - cron: '0 */4 * * *'
  workflow_dispatch:
    inputs:
      alert_level:
        description: 'Alert level for notifications'
        required: false
        default: 'normal'
        type: choice
        options:
        - normal
        - high
        - critical

env:
  MONITORING_ENABLED: true

jobs:
  continuous-health-monitor:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
        cache-dependency-path: backend/requirements.txt
        
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: Install monitoring dependencies
      run: |
        pip install requests psutil rich websockets
        
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y redis-server postgresql postgresql-contrib curl jq
        sudo systemctl start redis-server
        sudo systemctl start postgresql
        
    - name: Install application dependencies
      run: |
        cd backend && pip install -r requirements.txt
        cd ../frontend && npm ci --prefer-offline --no-audit
        
    - name: Setup environment
      run: |
        cp .env.example .env
        cd frontend && cp ../.env.development .env.local || echo "Using default frontend env"
        cd ../backend && cp ../.env .env || echo "Using default backend env"
        
    - name: Start services for monitoring
      run: |
        # Start backend
        nohup python -m uvicorn main:app --host 0.0.0.0 --port 8001 &
        echo $! > backend.pid
        
        # Start frontend
        cd frontend
        npm run build
        nohup npm start -- --port 3001 &
        echo $! > frontend.pid
        
        # Wait for services to start
        sleep 30
        
    - name: Run continuous health monitoring
      id: health-monitor
      run: |
        echo "🔍 Starting continuous health monitoring..."
        
        # Create monitoring script
        cat > monitor.py << 'EOF'
        import requests
        import json
        import time
        import sys
        from datetime import datetime
        
        def check_endpoint(url, name, timeout=10):
            try:
                start_time = time.time()
                response = requests.get(url, timeout=timeout)
                response_time = time.time() - start_time
                
                return {
                    "name": name,
                    "url": url,
                    "status": "healthy" if response.status_code == 200 else "unhealthy",
                    "status_code": response.status_code,
                    "response_time": round(response_time, 3),
                    "timestamp": datetime.now().isoformat()
                }
            except Exception as e:
                return {
                    "name": name,
                    "url": url,
                    "status": "error",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
        
        # Define endpoints to monitor
        endpoints = [
            ("http://localhost:8001/health", "Backend Health"),
            ("http://localhost:8001/api/health", "API Health"),
            ("http://localhost:3001", "Frontend"),
            ("http://localhost:8001/api/checklists", "Checklists API"),
            ("http://localhost:8001/api/reference-docs", "Reference Docs API")
        ]
        
        results = []
        failed_checks = 0
        
        print("Monitoring endpoints...")
        for url, name in endpoints:
            result = check_endpoint(url, name)
            results.append(result)
            
            status_icon = "✅" if result["status"] == "healthy" else "❌"
            print(f"{status_icon} {name}: {result['status']}")
            
            if result["status"] != "healthy":
                failed_checks += 1
        
        # Save results
        monitoring_data = {
            "timestamp": datetime.now().isoformat(),
            "total_checks": len(endpoints),
            "failed_checks": failed_checks,
            "success_rate": round((len(endpoints) - failed_checks) / len(endpoints) * 100, 2),
            "results": results
        }
        
        with open("monitoring-results.json", "w") as f:
            json.dump(monitoring_data, f, indent=2)
        
        print(f"\nMonitoring Summary:")
        print(f"Total checks: {monitoring_data['total_checks']}")
        print(f"Failed checks: {monitoring_data['failed_checks']}")
        print(f"Success rate: {monitoring_data['success_rate']}%")
        
        # Set output for GitHub Actions
        print(f"::set-output name=success_rate::{monitoring_data['success_rate']}")
        print(f"::set-output name=failed_checks::{monitoring_data['failed_checks']}")
        
        # Exit with error if too many failures
        if failed_checks > 2:
            print("❌ Too many failed checks!")
            sys.exit(1)
        elif failed_checks > 0:
            print("⚠️ Some checks failed but within acceptable limits")
            sys.exit(0)
        else:
            print("✅ All checks passed!")
            sys.exit(0)
        EOF
        
        python monitor.py
        
    - name: Performance monitoring
      run: |
        echo "📊 Running performance monitoring..."
        
        # Create performance monitoring script
        cat > performance_monitor.py << 'EOF'
        import requests
        import time
        import json
        import statistics
        from datetime import datetime
        
        def measure_response_times(url, iterations=5):
            times = []
            for i in range(iterations):
                try:
                    start = time.time()
                    response = requests.get(url, timeout=10)
                    end = time.time()
                    if response.status_code == 200:
                        times.append(end - start)
                    time.sleep(1)  # Wait between requests
                except:
                    pass
            
            if times:
                return {
                    "avg": round(statistics.mean(times), 3),
                    "min": round(min(times), 3),
                    "max": round(max(times), 3),
                    "count": len(times)
                }
            return None
        
        endpoints = [
            "http://localhost:8001/health",
            "http://localhost:8001/api/health",
            "http://localhost:3001"
        ]
        
        performance_data = {
            "timestamp": datetime.now().isoformat(),
            "measurements": {}
        }
        
        for url in endpoints:
            print(f"Measuring {url}...")
            perf = measure_response_times(url)
            if perf:
                performance_data["measurements"][url] = perf
                print(f"  Avg: {perf['avg']}s, Min: {perf['min']}s, Max: {perf['max']}s")
            else:
                print(f"  Failed to measure {url}")
        
        with open("performance-results.json", "w") as f:
            json.dump(performance_data, f, indent=2)
        EOF
        
        python performance_monitor.py
        
    - name: Generate monitoring report
      if: always()
      run: |
        echo "📋 Generating monitoring report..."
        
        # Create comprehensive report
        cat > monitoring_report.md << 'EOF'
        # Continuous Health Monitoring Report
        
        **Timestamp:** $(date)
        **Monitoring Run:** ${{ github.run_number }}
        **Alert Level:** ${{ github.event.inputs.alert_level || 'normal' }}
        
        ## Health Check Results
        
        EOF
        
        if [ -f monitoring-results.json ]; then
          echo "### Service Health Status" >> monitoring_report.md
          echo '```json' >> monitoring_report.md
          cat monitoring-results.json >> monitoring_report.md
          echo '```' >> monitoring_report.md
        fi
        
        if [ -f performance-results.json ]; then
          echo "### Performance Metrics" >> monitoring_report.md
          echo '```json' >> monitoring_report.md
          cat performance-results.json >> monitoring_report.md
          echo '```' >> monitoring_report.md
        fi
        
        # Add to GitHub Step Summary
        cat monitoring_report.md >> $GITHUB_STEP_SUMMARY
        
    - name: Send alerts if needed
      if: failure() || steps.health-monitor.outputs.failed_checks > 0
      run: |
        echo "🚨 Sending health monitoring alerts..."
        
        ALERT_LEVEL="${{ github.event.inputs.alert_level || 'normal' }}"
        FAILED_CHECKS="${{ steps.health-monitor.outputs.failed_checks || 0 }}"
        SUCCESS_RATE="${{ steps.health-monitor.outputs.success_rate || 0 }}"
        
        # Create alert payload
        cat > alert_payload.json << EOF
        {
          "timestamp": "$(date -Iseconds)",
          "alert_level": "$ALERT_LEVEL",
          "failed_checks": $FAILED_CHECKS,
          "success_rate": $SUCCESS_RATE,
          "repository": "${{ github.repository }}",
          "run_id": "${{ github.run_id }}",
          "run_url": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
        }
        EOF
        
        # Send to webhook if configured
        if [ -n "${{ secrets.ALERT_WEBHOOK_URL }}" ]; then
          curl -X POST "${{ secrets.ALERT_WEBHOOK_URL }}" \
            -H "Content-Type: application/json" \
            -d @alert_payload.json || echo "Failed to send webhook alert"
        fi
        
        # Send to Slack if configured
        if [ -n "${{ secrets.SLACK_WEBHOOK_URL }}" ]; then
          SLACK_MESSAGE="🚨 ComplianceMax Health Alert\n\nFailed Checks: $FAILED_CHECKS\nSuccess Rate: $SUCCESS_RATE%\nRun: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
          
          curl -X POST "${{ secrets.SLACK_WEBHOOK_URL }}" \
            -H "Content-Type: application/json" \
            -d "{\"text\":\"$SLACK_MESSAGE\"}" || echo "Failed to send Slack alert"
        fi
        
        echo "Alert notifications sent"
        
    - name: Upload monitoring artifacts
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: monitoring-results-${{ github.run_number }}
        path: |
          monitoring-results.json
          performance-results.json
          monitoring_report.md
          alert_payload.json
        retention-days: 30
        
    - name: Cleanup
      if: always()
      run: |
        # Stop services
        if [ -f backend/backend.pid ]; then
          kill $(cat backend/backend.pid) || true
        fi
        if [ -f frontend/frontend.pid ]; then
          kill $(cat frontend/frontend.pid) || true
        fi
        
        # Force cleanup
        pkill -f "uvicorn main:app" || true
        pkill -f "npm start" || true

  health-trend-analysis:
    runs-on: ubuntu-latest
    needs: continuous-health-monitor
    if: always()
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Download previous monitoring results
      uses: actions/download-artifact@v3
      with:
        name: monitoring-results-${{ github.run_number }}
        path: current-results/
      continue-on-error: true
      
    - name: Analyze health trends
      run: |
        echo "📈 Analyzing health trends..."
        
        # This would typically analyze historical data
        # For now, we'll create a basic trend report
        
        cat > trend_analysis.md << 'EOF'
        # Health Trend Analysis
        
        **Analysis Date:** $(date)
        **Run Number:** ${{ github.run_number }}
        
        ## Trend Summary
        
        This analysis compares current health metrics with historical data.
        
        ### Key Metrics
        - Current success rate: ${SUCCESS_RATE:-N/A}%
        - Failed checks: ${FAILED_CHECKS:-N/A}
        
        ### Recommendations
        
        EOF
        
        # Get values from the previous job
        SUCCESS_RATE="${{ needs.continuous-health-monitor.outputs.success_rate || '0' }}"
        FAILED_CHECKS="${{ needs.continuous-health-monitor.outputs.failed_checks || '0' }}"
        
        # Add recommendations based on results
        if [ "$FAILED_CHECKS" -gt "0" ]; then
          echo "- ⚠️ Investigate failed health checks" >> trend_analysis.md
          echo "- 🔍 Review service logs for errors" >> trend_analysis.md
          echo "- 🛠️ Consider scaling or optimization" >> trend_analysis.md
        else
          echo "- ✅ All systems operating normally" >> trend_analysis.md
          echo "- 📊 Continue monitoring for trends" >> trend_analysis.md
        fi
        
        # Add to GitHub Step Summary
        cat trend_analysis.md >> $GITHUB_STEP_SUMMARY

