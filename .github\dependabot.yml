version: 2
updates:
  - package-ecosystem: "pip"
    directory: "/backend/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
    reviewers:
      - "compliancemax-team"
    assignees:
      - "compliancemax-admin"
    commit-message:
      prefix: "deps"
      include: "scope"

  - package-ecosystem: "npm"
    directory: "/frontend/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
    reviewers:
      - "compliancemax-team"
    assignees:
      - "compliancemax-admin"
    commit-message:
      prefix: "deps"
      include: "scope"

  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 5 